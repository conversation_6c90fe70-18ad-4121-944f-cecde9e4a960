
// Truck Types (Cavalo do Caminhão)
export enum TruckType {
  GAS = "Gás",
  ELECTRIC = "Elétrica",
  RETRACTABLE = "Retrátil"
}

export enum TruckStatus {
  OPERATIONAL = "Em Operação",
  STOPPED = "Parada",
  MAINTENANCE = "Aguardando Manutenção"
}

export interface Truck {
  id: string;
  model: string;
  type: TruckType;
  capacity: string;
  acquisitionDate: string;
  lastMaintenance: string;
  status: TruckStatus;
  hourMeter: number;

}

// Legacy aliases for backward compatibility
export const ForkliftType = TruckType;
export const ForkliftStatus = TruckStatus;
export type Forklift = Truck;

// User/Operator Types
export enum UserRole {
  OPERATOR = "Motorista",
  SUPERVISOR = "Supervisor",
  ADMIN = "Administrador"
}

export enum CertificateStatus {
  REGULAR = "Regular",
  WARNING = "Próximo do Vencimento",
  EXPIRED = "Vencido"
}

export enum DocumentStatus {
  ISSUED = "Emitido",
  PENDING = "Pendente",
  MISSING = "Sem Documento"
}

export interface User {
  id: string;
  name: string;
  role: UserRole;
  cpf: string;
  contact: string;
  shift: string;
  registrationDate: string;
  asoExpirationDate: string;
  nrExpirationDate: string;
  asoStatus: CertificateStatus;
  nrStatus: CertificateStatus;
}

// Operation Types
export enum ClienteEnum {
  NOVELIS = "Novelis",
  PEPSICO = "Pepsico",
  AMBEV = "Ambev"
}

export enum ModalidadeEnum {
  FROTA = "Frota",
  TERCEIRO = "Terceiro",
  AGREGADO = "Agregado"
}

export enum StatusCarregamentoEnum {
  CARREGANDO = "Carregando",
  CARREGADO = "Carregado",
  NO_PRAZO = "NO PRAZO",
  ATRASADO = "ATRASADO",
  CANCELADO = "Cancelado"
}

export enum StatusDescargaEnum {
  AGUARDANDO = "Aguardando",
  DESCARREGANDO = "Descarregando",
  DESCARREGADO = "Descarregado",
  NO_PRAZO = "NO PRAZO",
  ATRASADO = "ATRASADO",
  CANCELADO = "Cancelado"
}

export interface Operation {
  // Identificação
  chave: string;

  // Agendamento
  dataHoraAgendadaCarregamento: string;
  dataHoraAgendadaDescarga: string;

  // Produto e Cliente
  produto: string;
  clientePagador: ClienteEnum;
  idB100SvnDt: string;

  // Origem e Destino
  clienteCidadeOrigem: string;
  ufOrigem: string;
  clienteCidadeDestino: string;
  ufDestino: string;
  peso: string;

  // Motorista e Equipamento
  motorista: string;
  modalidade: ModalidadeEnum;
  diasEmRota: number;
  modeloEquipamento: string;

  // Operação
  operacaoCadastrada: boolean;
  codigoDaOperacao?: string;
  numeroRota?: string;

  // Valores Financeiros
  freteEmpresaNet?: string;
  freteTerceiro?: string;
  margemTerceiro?: string;
  freteAgregado?: string;
  margemAgregado?: string;

  // Logística
  numeroSolicitacaoCarga?: string;
  placaTracao?: string;
  programador?: string;

  // Carregamento
  appClienteCarga?: string;
  dataHoraChegadaCarga?: string;
  dataHoraInicioCarga?: string;
  dataHoraFimCarregamento?: string;
  tempoEmCarga?: string;
  statusFinalCarregamento?: StatusCarregamentoEnum;
  diariasC?: string;
  performanceCarregamento?: string;
  causaAtrazoCarregamento?: string;

  // Documentação
  numeroNotaFiscal?: string;
  numeroCTE?: string;
  numeroOcorrenciaCarga?: string;

  // Descarga
  appClienteDescarga?: string;
  dataHoraChegadaDescarga?: string;
  dataHoraInicioDescarga?: string;
  dataHoraFimDescarga?: string;
  tempoDescarga?: string;
  statusFinal?: StatusDescargaEnum;
  performanceDescarga?: string;
  causaAtrazoDescarga?: string;
  diariasD?: string;
  numeroOcorrenciaDescarga?: string;

  // Finalização
  houveDescarga?: boolean;
  devolucaoPallet?: string;
  houveAvaria?: boolean;
  houveDevolucao?: boolean;
  comprovanteDescarga?: string;
  observacao?: string;
}

// Maintenance Types
export enum MaintenanceStatus {
  WAITING = "Aguardando",
  IN_PROGRESS = "Em andamento",
  COMPLETED = "Concluído"
}

export interface Maintenance {
  id: string;
  truckId: string;
  truckModel: string;
  issue: string;
  reportedBy: string;
  reportedDate: string;
  status: MaintenanceStatus;
  completedDate?: string;
  // Legacy aliases for backward compatibility
  forkliftId?: string;
  forkliftModel?: string;
}

// Gas Supply Types
export interface GasSupply {
  id: string;
  date: string;
  truckId: string;
  truckModel: string;
  quantity: number;
  hourMeterBefore: number;
  hourMeterAfter: number;
  operator: string;
  // Legacy aliases for backward compatibility
  forkliftId?: string;
  forkliftModel?: string;
}

// Dashboard Types
export interface DashboardStats {
  totalTrucks: number;
  operationalTrucks: number;
  stoppedTrucks: number;
  maintenanceTrucks: number;
  totalOperators: number;
  operatorsWithValidCertificates: number;
  operatorsWithWarningCertificates: number;
  operatorsWithExpiredCertificates: number;
  activeOperations: number;
  pendingMaintenances: number;
  cteIssued: number;
  nfIssued: number;
  withoutCte: number;
  // Legacy aliases for backward compatibility
  totalForklifts?: number;
  operationalForklifts?: number;
  stoppedForklifts?: number;
  maintenanceForklifts?: number;
}

// Column Configuration Types
export interface ColumnConfig {
  key: keyof Operation;
  label: string;
  visible: boolean;
  width?: string;
  category: 'identificacao' | 'agendamento' | 'produto' | 'rota' | 'motorista' | 'operacao' | 'financeiro' | 'carregamento' | 'descarga' | 'documentos';
}

export interface TableColumn {
  key: keyof Operation;
  label: string;
  width?: string;
  render?: (value: any, operation: Operation) => React.ReactNode;
}

// Common Component Props
export interface StatusCardProps {
  title: string;
  value: number;
  icon: React.ElementType;
  status?: "success" | "warning" | "danger" | "info" | "neutral";
  change?: {
    value: number;
    trend: "up" | "down" | "neutral";
  };
}
