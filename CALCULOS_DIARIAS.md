# 💰 Sistema de Cálculo de Diárias - Documentação

## 🎯 Visão Geral

O sistema de cálculo de diárias foi desenvolvido para calcular automaticamente o tempo de atraso causado pelo **cliente** (não pela transportadora) durante as operações de carregamento e descarregamento. Essas diárias representam custos adicionais que podem ser cobrados do cliente.

## 📊 Tipos de Diárias

### 🚛 **Diárias C (Carregamento)**
**Definição**: Tempo de atraso do cliente no processo de carregamento

**Regra de Cálculo**:
- **Início**: Hor<PERSON>rio agendado para carregamento (independente da chegada)
- **Fim**: Horário real de fim do carregamento
- **Exceção**: Se o caminhão chegou adiantado, o tempo adiantado é desconsiderado

### 🏭 **Diárias D (Descarregamento)**
**Definição**: Tempo de atraso do cliente no processo de descarregamento + possível herança do carregamento

**Regra de Cálculo**:
- **Início**: Hor<PERSON>rio agendado para descarga (ajustado por atrasos herdados)
- **Fim**: Horário real de fim da descarga
- **Herança**: Considera atrasos causados pelo cliente do carregamento

## 🧮 **Lógica de Cálculo Detalhada**

### 📦 **Diárias C - Carregamento**

#### ✅ **Cenário 1: Chegada Pontual**
```
Agendado Carregamento: 07:00:00
Chegada:              06:45:00 (adiantado - desconsiderado)
Fim Carregamento:     10:30:00

Cálculo: 10:30:00 - 07:00:00 = 3h 30min
Diárias C: 3h 30min
```

#### ✅ **Cenário 2: Chegada Atrasada**
```
Agendado Carregamento: 07:00:00
Chegada:              08:00:00 (atrasado)
Fim Carregamento:     10:30:00

Cálculo: 10:30:00 - 07:00:00 = 3h 30min
Diárias C: 3h 30min
(O atraso na chegada não afeta o cálculo da diária do cliente)
```

#### ❌ **Cenário 3: Sem Atraso do Cliente**
```
Agendado Carregamento: 07:00:00
Chegada:              06:45:00
Fim Carregamento:     07:00:00

Cálculo: 07:00:00 - 07:00:00 = 0h
Diárias C: 0h
```

### 🏭 **Diárias D - Descarregamento**

#### ✅ **Cenário 1: Sem Herança, Com Atraso**
```
Agendado Descarga: 22:00:00
Chegada Descarga:  21:45:00
Fim Descarga:      01:30:00

Cálculo: 01:30:00 - 22:00:00 = 3h 30min
Diárias D: 3h 30min
```

#### 🔄 **Cenário 2: Com Herança do Carregamento**
```
CARREGAMENTO:
Agendado: 07:00:00
Fim:      10:30:00
Atraso:   3h 30min

DESCARGA:
Agendado Original: 22:00:00
Agendado Ajustado: 01:30:00 (22:00 + 3h30min)
Chegada:          01:15:00
Fim:              04:00:00

Cálculo: 04:00:00 - 01:30:00 = 2h 30min
Diárias D: 2h 30min
```

#### 🔄 **Cenário 3: Herança + Atraso Adicional**
```
CARREGAMENTO:
Agendado: 07:00:00
Fim:      10:30:00
Atraso:   3h 30min

DESCARGA:
Agendado Original: 22:00:00
Agendado Ajustado: 01:30:00 (22:00 + 3h30min)
Chegada:          02:00:00 (ainda mais atrasado)
Fim:              05:30:00

Cálculo: 05:30:00 - 02:00:00 = 3h 30min
Diárias D: 3h 30min
```

## 🎯 **Exemplos Práticos Completos**

### 🟢 **Operação Sem Diárias**
```
CARREGAMENTO:
- Agendado: 05/06/2025 07:00:00
- Chegada:  05/06/2025 06:45:00
- Fim:      05/06/2025 07:00:00
- Diárias C: 0h

DESCARGA:
- Agendado: 09/06/2025 22:00:00
- Chegada:  09/06/2025 21:45:00
- Fim:      09/06/2025 22:00:00
- Diárias D: 0h

Total de Diárias: 0h
```

### 🔴 **Operação Com Diárias Altas**
```
CARREGAMENTO:
- Agendado: 05/06/2025 07:00:00
- Chegada:  05/06/2025 06:45:00
- Fim:      05/06/2025 12:00:00
- Diárias C: 5h

DESCARGA:
- Agendado Original: 09/06/2025 22:00:00
- Agendado Ajustado: 10/06/2025 03:00:00
- Chegada:  10/06/2025 02:45:00
- Fim:      10/06/2025 08:00:00
- Diárias D: 5h

Total de Diárias: 10h
```

### 🟡 **Operação Mista**
```
CARREGAMENTO:
- Agendado: 05/06/2025 07:00:00
- Chegada:  05/06/2025 08:00:00 (atraso do motorista)
- Fim:      05/06/2025 10:30:00
- Diárias C: 3h 30min (cliente ainda é responsável)

DESCARGA:
- Agendado Original: 09/06/2025 22:00:00
- Agendado Ajustado: 10/06/2025 01:30:00
- Chegada:  10/06/2025 01:30:00 (pontual considerando herança)
- Fim:      10/06/2025 01:30:00
- Diárias D: 0h

Total de Diárias: 3h 30min
```

## 🔧 **Implementação Técnica**

### 📁 **Funções Principais**
```typescript
// Diárias de carregamento
calculateLoadingDemurrage(
  scheduledDateTime: string,
  arrivalDateTime: string,
  endDateTime: string
): string

// Diárias de descarga
calculateUnloadingDemurrage(
  loadingScheduledDateTime: string,
  loadingArrivalDateTime: string,
  loadingEndDateTime: string,
  unloadingScheduledDateTime: string,
  unloadingArrivalDateTime: string,
  unloadingEndDateTime: string
): string
```

### ⚡ **Gatilhos de Recálculo**
Os cálculos são executados automaticamente quando alterados:
- `dataHoraAgendadaCarregamento`
- `dataHoraChegadaCarga`
- `dataHoraFimCarregamento`
- `dataHoraAgendadaDescarga`
- `dataHoraChegadaDescarga`
- `dataHoraFimDescarga`

### 🎨 **Interface do Usuário**
- **Campos somente leitura** com fundo acinzentado
- **Cálculo em tempo real** conforme dados são preenchidos
- **Formato consistente**: "Xh Ymin" ou "0h"

## 💡 **Regras de Negócio**

### ✅ **Princípios Fundamentais**
1. **Responsabilidade do Cliente**: Diárias são cobradas apenas por atrasos causados pelo cliente
2. **Herança de Atrasos**: Atrasos do carregamento afetam o cálculo da descarga
3. **Desconsideração de Adiantamentos**: Chegadas adiantadas não reduzem diárias
4. **Transparência**: Todos os cálculos são automáticos e auditáveis

### 🚫 **O que NÃO é Cobrado**
- Atrasos na chegada do caminhão
- Tempo de viagem entre carregamento e descarga
- Atrasos causados por problemas da transportadora
- Tempo de espera dentro do horário agendado

### ✅ **O que É Cobrado**
- Tempo extra de carregamento além do agendado
- Tempo extra de descarga além do agendado (ajustado por heranças)
- Atrasos causados por demora do cliente em liberar a carga
- Atrasos causados por problemas no local do cliente

## 📈 **Benefícios do Sistema**

### 💰 **Financeiros**
- **Cobrança justa** baseada em tempo real de atraso
- **Transparência** nos cálculos para o cliente
- **Redução de disputas** com dados objetivos
- **Otimização de receita** com cobrança automática

### 📊 **Operacionais**
- **Eliminação de erros** de cálculo manual
- **Padronização** de critérios em toda a operação
- **Relatórios precisos** de performance por cliente
- **Identificação** de clientes problemáticos

### 🎯 **Estratégicos**
- **Incentivo à pontualidade** dos clientes
- **Dados para negociação** de contratos
- **Melhoria contínua** dos processos
- **Competitividade** com preços justos

---

**Sistema desenvolvido para garantir cobrança justa e transparente de diárias baseada em dados objetivos e cálculos automáticos.**
