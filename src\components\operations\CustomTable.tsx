import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Eye } from 'lucide-react';
import { Operation, ColumnConfig, StatusCarregamentoEnum, StatusDescargaEnum, ClienteEnum, ModalidadeEnum } from '@/types';

interface CustomTableProps {
  operations: Operation[];
  columns: ColumnConfig[];
  onViewDetails: (operation: Operation) => void;
  onEdit: (operation: Operation) => void;
}

const CustomTable = ({ operations, columns, onViewDetails, onEdit }: CustomTableProps) => {
  const visibleColumns = columns.filter(col => col.visible);

  // Format date/time from Brazilian format
  const formatDateTime = (dateTimeString?: string) => {
    if (!dateTimeString) return '-';
    return dateTimeString;
  };

  // Get status color for carregamento
  const getStatusColor = (status?: StatusCarregamentoEnum | StatusDescargaEnum) => {
    switch (status) {
      case StatusCarregamentoEnum.CARREGADO:
      case StatusDescargaEnum.DESCARREGADO:
      case StatusCarregamentoEnum.NO_PRAZO:
      case StatusDescargaEnum.NO_PRAZO:
        return 'bg-status-operational/10 text-status-operational';
      case StatusCarregamentoEnum.CARREGANDO:
      case StatusDescargaEnum.DESCARREGANDO:
        return 'bg-status-maintenance/10 text-status-maintenance';
      case StatusCarregamentoEnum.ATRASADO:
      case StatusDescargaEnum.ATRASADO:
        return 'bg-status-warning/10 text-status-warning';
      case StatusDescargaEnum.AGUARDANDO:
        return 'bg-yellow-100 text-yellow-800';
      case StatusCarregamentoEnum.CANCELADO:
      case StatusDescargaEnum.CANCELADO:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  // Render cell value based on column type
  const renderCellValue = (operation: Operation, column: ColumnConfig) => {
    const value = operation[column.key];

    switch (column.key) {
      case 'clientePagador':
        return <Badge variant="outline">{value as ClienteEnum}</Badge>;
      
      case 'modalidade':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
            {value as ModalidadeEnum}
          </span>
        );
      
      case 'statusFinalCarregamento':
      case 'statusFinal':
        return (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getStatusColor(value as StatusCarregamentoEnum | StatusDescargaEnum)}`}>
            {value || 'Agendado'}
          </span>
        );
      
      case 'operacaoCadastrada':
      case 'houveDescarga':
      case 'houveAvaria':
      case 'houveDevolucao':
        return (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
            value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {value ? 'Sim' : 'Não'}
          </span>
        );
      
      case 'dataHoraAgendadaCarregamento':
      case 'dataHoraAgendadaDescarga':
      case 'dataHoraChegadaCarga':
      case 'dataHoraInicioCarga':
      case 'dataHoraFimCarregamento':
      case 'dataHoraChegadaDescarga':
      case 'dataHoraInicioDescarga':
      case 'dataHoraFimDescarga':
        return <span className="text-sm">{formatDateTime(value as string)}</span>;
      
      case 'freteEmpresaNet':
      case 'freteTerceiro':
      case 'margemTerceiro':
      case 'freteAgregado':
      case 'margemAgregado':
        return <span className="font-mono text-sm">{value || '-'}</span>;
      
      case 'diasEmRota':
        return <span className="text-center">{value || 0}</span>;
      
      default:
        return <span className="text-sm">{value || '-'}</span>;
    }
  };

  if (operations.length === 0) {
    return (
      <div className="bg-card rounded-lg shadow overflow-hidden">
        <div className="p-8 text-center">
          <p className="text-muted-foreground">Nenhuma operação encontrada</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50">
            <tr>
              {visibleColumns.map((column) => (
                <th 
                  key={column.key} 
                  className="p-4 text-left font-medium text-muted-foreground"
                  style={{ width: column.width }}
                >
                  {column.label}
                </th>
              ))}
              <th className="p-4 text-left font-medium text-muted-foreground w-32">
                Ações
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {operations.map((operation) => (
              <tr key={operation.chave} className="hover:bg-muted/50 transition-colors">
                {visibleColumns.map((column) => (
                  <td key={column.key} className="p-4">
                    {renderCellValue(operation, column)}
                  </td>
                ))}
                <td className="p-4">
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewDetails(operation)}
                      title="Ver detalhes"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(operation)}
                      title="Editar"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CustomTable;
