import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Operation, ClienteEnum, ModalidadeEnum, StatusCarregamentoEnum, StatusDescargaEnum } from '@/types';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Calendar, 
  Clock, 
  Truck, 
  User, 
  MapPin, 
  Package, 
  FileText, 
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface OperationDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operation: Operation | null;
}

const OperationDetails = ({ open, onOpenChange, operation }: OperationDetailsProps) => {
  if (!operation) return null;

  const formatDateTime = (dateTime: string) => {
    if (!dateTime) return '-';
    return dateTime;
  };

  const getStatusBadge = (status: string | undefined, type: 'carregamento' | 'descarga') => {
    if (!status) return <Badge variant="secondary">Não definido</Badge>;
    
    const statusConfig = {
      [StatusCarregamentoEnum.CARREGANDO]: { variant: 'default', color: 'bg-blue-500' },
      [StatusCarregamentoEnum.CARREGADO]: { variant: 'default', color: 'bg-green-500' },
      [StatusCarregamentoEnum.NO_PRAZO]: { variant: 'default', color: 'bg-green-500' },
      [StatusCarregamentoEnum.ATRASADO]: { variant: 'destructive', color: 'bg-red-500' },
      [StatusCarregamentoEnum.CANCELADO]: { variant: 'secondary', color: 'bg-gray-500' },
      [StatusDescargaEnum.AGUARDANDO]: { variant: 'secondary', color: 'bg-yellow-500' },
      [StatusDescargaEnum.DESCARREGANDO]: { variant: 'default', color: 'bg-blue-500' },
      [StatusDescargaEnum.DESCARREGADO]: { variant: 'default', color: 'bg-green-500' },
      [StatusDescargaEnum.NO_PRAZO]: { variant: 'default', color: 'bg-green-500' },
      [StatusDescargaEnum.ATRASADO]: { variant: 'destructive', color: 'bg-red-500' },
      [StatusDescargaEnum.CANCELADO]: { variant: 'secondary', color: 'bg-gray-500' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', color: 'bg-gray-500' };
    return <Badge variant={config.variant as any}>{status}</Badge>;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[1200px] h-[85vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Detalhes da Operação - {operation.chave}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="geral" className="w-full flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-6 flex-shrink-0">
            <TabsTrigger value="geral">Geral</TabsTrigger>
            <TabsTrigger value="carregamento">Carregamento</TabsTrigger>
            <TabsTrigger value="descarga">Descarga</TabsTrigger>
            <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
            <TabsTrigger value="documentos">Documentos</TabsTrigger>
            <TabsTrigger value="observacoes">Observações</TabsTrigger>
          </TabsList>

          {/* Aba Geral */}
          <TabsContent value="geral" className="flex-1 overflow-hidden">
            <ScrollArea className="h-[calc(85vh-200px)] pr-4">
              <div className="space-y-6 pb-4">
                {/* Identificação */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Identificação
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Chave</div>
                      <div className="text-sm font-medium">{operation.chave}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Cliente Pagador</div>
                      <Badge variant="outline">{operation.clientePagador}</Badge>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">ID/B100/SVN/Nº DT</div>
                      <div className="text-sm font-medium">{operation.idB100SvnDt || '-'}</div>
                    </div>
                  </div>
                </div>

                {/* Agendamento */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Agendamento
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Agendada P/ Carregamento</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraAgendadaCarregamento)}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Agendada P/ Descarga</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraAgendadaDescarga)}</div>
                    </div>
                  </div>
                </div>

                {/* Produto e Rota */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Package className="w-5 h-5" />
                    Produto e Rota
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Produto</div>
                      <div className="text-sm font-medium">{operation.produto}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Peso</div>
                      <div className="text-sm font-medium">{operation.peso || '-'}</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Cidade Origem</div>
                      <div className="text-sm font-medium">{operation.clienteCidadeOrigem || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">UF Origem</div>
                      <div className="text-sm font-medium">{operation.ufOrigem || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Cidade Destino</div>
                      <div className="text-sm font-medium">{operation.clienteCidadeDestino || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">UF Destino</div>
                      <div className="text-sm font-medium">{operation.ufDestino || '-'}</div>
                    </div>
                  </div>
                </div>

                {/* Motorista e Equipamento */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <User className="w-5 h-5" />
                    Motorista e Equipamento
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Motorista</div>
                      <div className="text-sm font-medium">{operation.motorista}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Modelo/Equipamento</div>
                      <div className="text-sm font-medium">{operation.modeloEquipamento || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Modalidade</div>
                      <Badge variant="outline">{operation.modalidade}</Badge>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Dias em Rota</div>
                      <div className="text-sm font-medium">{operation.diasEmRota || 0}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Programador</div>
                      <div className="text-sm font-medium">{operation.programador || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Placa/Tração</div>
                      <div className="text-sm font-medium">{operation.placaTracao || '-'}</div>
                    </div>
                  </div>
                </div>

                {/* Operação e Códigos */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Truck className="w-5 h-5" />
                    Operação e Códigos
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Código da Operação</div>
                      <div className="text-sm font-medium">{operation.codigoDaOperacao || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Nº Rota</div>
                      <div className="text-sm font-medium">{operation.numeroRota || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Nº Solicitação de Carga</div>
                      <div className="text-sm font-medium">{operation.numeroSolicitacaoCarga || '-'}</div>
                    </div>
                  </div>
                  <div className="p-3 bg-muted/20 rounded-md">
                    <div className="text-xs text-muted-foreground mb-1">Operação Cadastrada</div>
                    <div className="flex items-center gap-2">
                      {operation.operacaoCadastrada ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500" />
                      )}
                      <span className="text-sm font-medium">
                        {operation.operacaoCadastrada ? 'Sim' : 'Não'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Aba Carregamento */}
          <TabsContent value="carregamento" className="flex-1 overflow-hidden">
            <ScrollArea className="h-[calc(85vh-200px)] pr-4">
              <div className="space-y-6 pb-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Truck className="w-5 h-5" />
                    Informações de Carregamento
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">App Cliente - Carga</div>
                      <div className="text-sm font-medium">{operation.appClienteCarga || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Status Final do Carregamento</div>
                      <div>{getStatusBadge(operation.statusFinalCarregamento, 'carregamento')}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Horários de Carregamento
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Chegada Carga</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraChegadaCarga)}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Início de Carga</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraInicioCarga)}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Fim de Carregamento</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraFimCarregamento)}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Performance e Controle</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Tempo em Carga</div>
                      <div className="text-sm font-medium">{operation.tempoEmCarga || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Performance de Carregamento</div>
                      <div className="text-sm font-medium">{operation.performanceCarregamento || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Diárias C</div>
                      <div className="text-sm font-medium">{operation.diariasC || '-'}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    Ocorrências
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Causa do Atraso</div>
                      <div className="text-sm font-medium">{operation.causaAtrazoCarregamento || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Nº Ocorrência de Carga</div>
                      <div className="text-sm font-medium">{operation.numeroOcorrenciaCarga || '-'}</div>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Aba Descarga */}
          <TabsContent value="descarga" className="flex-1 overflow-hidden">
            <ScrollArea className="h-[calc(85vh-200px)] pr-4">
              <div className="space-y-6 pb-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Truck className="w-5 h-5" />
                    Informações de Descarga
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">App Cliente - Descarga</div>
                      <div className="text-sm font-medium">{operation.appClienteDescarga || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Status Final</div>
                      <div>{getStatusBadge(operation.statusFinal, 'descarga')}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Horários de Descarga
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Chegada P/Descarga</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraChegadaDescarga)}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Início de Descarga</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraInicioDescarga)}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Data/Hora Fim de Descarga</div>
                      <div className="text-sm font-medium">{formatDateTime(operation.dataHoraFimDescarga)}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Performance e Controle</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Tempo de Descarga</div>
                      <div className="text-sm font-medium">{operation.tempoDescarga || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Performance de Descarga</div>
                      <div className="text-sm font-medium">{operation.performanceDescarga || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Diárias D</div>
                      <div className="text-sm font-medium">{operation.diariasD || '-'}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    Ocorrências e Finalização
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Causa do Atraso</div>
                      <div className="text-sm font-medium">{operation.causaAtrazoDescarga || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Nº Ocorrência Descarga</div>
                      <div className="text-sm font-medium">{operation.numeroOcorrenciaDescarga || '-'}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Devolução de Pallet</div>
                      <div className="text-sm font-medium">{operation.devolucaoPallet || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Comprovante de Descarga</div>
                      <div className="text-sm font-medium">{operation.comprovanteDescarga || '-'}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Houve Descarga?</div>
                      <div className="flex items-center gap-2">
                        {operation.houveDescarga ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                        <span className="text-sm font-medium">
                          {operation.houveDescarga ? 'Sim' : 'Não'}
                        </span>
                      </div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Houve Avaria?</div>
                      <div className="flex items-center gap-2">
                        {operation.houveAvaria ? (
                          <XCircle className="w-4 h-4 text-red-500" />
                        ) : (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                        <span className="text-sm font-medium">
                          {operation.houveAvaria ? 'Sim' : 'Não'}
                        </span>
                      </div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Houve Devolução?</div>
                      <div className="flex items-center gap-2">
                        {operation.houveDevolucao ? (
                          <AlertTriangle className="w-4 h-4 text-yellow-500" />
                        ) : (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                        <span className="text-sm font-medium">
                          {operation.houveDevolucao ? 'Sim' : 'Não'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Aba Financeiro */}
          <TabsContent value="financeiro" className="flex-1 overflow-hidden">
            <ScrollArea className="h-[calc(85vh-200px)] pr-4">
              <div className="space-y-6 pb-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    Valores Financeiros
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Frete Empresa NET</div>
                      <div className="text-sm font-medium">{operation.freteEmpresaNet || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Frete Terceiro</div>
                      <div className="text-sm font-medium">{operation.freteTerceiro || '-'}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Margem Terceiro</div>
                      <div className="text-sm font-medium">{operation.margemTerceiro || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Frete Agregado</div>
                      <div className="text-sm font-medium">{operation.freteAgregado || '-'}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Margem Agregado</div>
                      <div className="text-sm font-medium">{operation.margemAgregado || '-'}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Resumo por Modalidade</h3>
                  <div className="p-4 border rounded-md bg-muted/30">
                    <div className="text-sm font-medium mb-2">Modalidade: {operation.modalidade}</div>
                    <div className="text-xs text-muted-foreground">
                      {operation.modalidade === ModalidadeEnum.FROTA && 'Valores de frete da empresa própria'}
                      {operation.modalidade === ModalidadeEnum.TERCEIRO && 'Valores de frete para terceiros com margem aplicada'}
                      {operation.modalidade === ModalidadeEnum.AGREGADO && 'Valores de frete para agregados com margem aplicada'}
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Aba Documentos */}
          <TabsContent value="documentos" className="flex-1 overflow-hidden">
            <ScrollArea className="h-[calc(85vh-200px)] pr-4">
              <div className="space-y-6 pb-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Documentação Fiscal
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Número da Nota Fiscal</div>
                      <div className="text-sm font-medium">{operation.numeroNotaFiscal || '-'}</div>
                    </div>
                    <div className="p-3 bg-muted/20 rounded-md">
                      <div className="text-xs text-muted-foreground mb-1">Número CTE</div>
                      <div className="text-sm font-medium">{operation.numeroCTE || '-'}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-md">
                    <div className="text-sm font-medium mb-2 text-yellow-800 flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4" />
                      Documentação Obrigatória
                    </div>
                    <ul className="text-xs text-yellow-700 space-y-1">
                      <li>• CTE é obrigatório para transporte de cargas</li>
                      <li>• Motorista não deve sair sem aguardar a emissão do CTE</li>
                      <li>• Nota Fiscal deve acompanhar a mercadoria</li>
                      <li>• Verificar se todos os documentos estão corretos antes da saída</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Status dos Documentos</h3>
                  <div className="p-4 border rounded-md">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Nota Fiscal:</span>
                        <span className={operation.numeroNotaFiscal ? 'text-green-600 flex items-center gap-1' : 'text-gray-500 flex items-center gap-1'}>
                          {operation.numeroNotaFiscal ? (
                            <>
                              <CheckCircle className="w-4 h-4" />
                              Emitida
                            </>
                          ) : (
                            <>
                              <Clock className="w-4 h-4" />
                              Pendente
                            </>
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>CTE:</span>
                        <span className={operation.numeroCTE ? 'text-green-600 flex items-center gap-1' : 'text-yellow-600 flex items-center gap-1'}>
                          {operation.numeroCTE ? (
                            <>
                              <CheckCircle className="w-4 h-4" />
                              Emitido
                            </>
                          ) : (
                            <>
                              <AlertTriangle className="w-4 h-4" />
                              Sem CTE
                            </>
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Aba Observações */}
          <TabsContent value="observacoes" className="flex-1 overflow-hidden">
            <ScrollArea className="h-[calc(85vh-200px)] pr-4">
              <div className="space-y-6 pb-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Observações Gerais</h3>
                  <div className="p-4 border rounded-md bg-muted/20">
                    <div className="text-sm whitespace-pre-wrap">
                      {operation.observacao || 'Nenhuma observação registrada.'}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Resumo da Operação</h3>
                  <div className="p-4 border rounded-md bg-muted/30">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Cliente:</span>
                        <span>{operation.clientePagador || '-'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Motorista:</span>
                        <span>{operation.motorista || '-'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Rota:</span>
                        <span>
                          {operation.clienteCidadeOrigem && operation.clienteCidadeDestino
                            ? `${operation.clienteCidadeOrigem}/${operation.ufOrigem} → ${operation.clienteCidadeDestino}/${operation.ufDestino}`
                            : '-'
                          }
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Modalidade:</span>
                        <span>{operation.modalidade || '-'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Produto:</span>
                        <span>{operation.produto || '-'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Peso:</span>
                        <span>{operation.peso || '-'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default OperationDetails;
