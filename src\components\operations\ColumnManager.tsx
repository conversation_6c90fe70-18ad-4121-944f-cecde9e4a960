import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { ColumnConfig } from '@/types';
import { Settings, Eye, EyeOff } from 'lucide-react';

interface ColumnManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  columns: ColumnConfig[];
  onColumnsChange: (columns: ColumnConfig[]) => void;
}

const ColumnManager = ({ open, onOpenChange, columns, onColumnsChange }: ColumnManagerProps) => {
  const [localColumns, setLocalColumns] = useState<ColumnConfig[]>(columns);

  // Group columns by category
  const groupedColumns = localColumns.reduce((acc, column) => {
    if (!acc[column.category]) {
      acc[column.category] = [];
    }
    acc[column.category].push(column);
    return acc;
  }, {} as Record<string, ColumnConfig[]>);

  const categoryLabels = {
    identificacao: 'Identificação',
    agendamento: 'Agendamento',
    produto: 'Produto e Rota',
    rota: 'Origem e Destino',
    motorista: 'Motorista e Equipamento',
    operacao: 'Operação e Códigos',
    financeiro: 'Financeiro',
    carregamento: 'Carregamento',
    descarga: 'Descarga',
    documentos: 'Documentos'
  };

  const handleColumnToggle = (columnKey: keyof typeof localColumns[0], checked: boolean) => {
    setLocalColumns(prev =>
      prev.map(col =>
        col.key === columnKey ? { ...col, visible: checked } : col
      )
    );
  };

  const handleSelectAll = (category: string, checked: boolean) => {
    setLocalColumns(prev =>
      prev.map(col =>
        col.category === category ? { ...col, visible: checked } : col
      )
    );
  };

  const handleSave = () => {
    onColumnsChange(localColumns);
    onOpenChange(false);
  };

  const handleReset = () => {
    // Reset to default visible columns
    const defaultColumns = localColumns.map(col => ({
      ...col,
      visible: ['chave', 'clientePagador', 'motorista', 'clienteCidadeOrigem', 'clienteCidadeDestino', 'produto', 'peso', 'modalidade', 'statusFinalCarregamento'].includes(col.key)
    }));
    setLocalColumns(defaultColumns);
  };

  const visibleCount = localColumns.filter(col => col.visible).length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] h-[85vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Configurar Colunas da Tabela
          </DialogTitle>
          <div className="text-sm text-muted-foreground">
            Selecione quais colunas deseja exibir na tabela de operações
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {visibleCount} de {localColumns.length} colunas selecionadas
              </Badge>
            </div>
            <Button variant="outline" size="sm" onClick={handleReset}>
              Restaurar Padrão
            </Button>
          </div>

          <ScrollArea className="h-[calc(85vh-200px)]">
            <div className="space-y-6">
              {Object.entries(groupedColumns).map(([category, categoryColumns]) => {
                const visibleInCategory = categoryColumns.filter(col => col.visible).length;
                const totalInCategory = categoryColumns.length;
                
                return (
                  <div key={category} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">
                        {categoryLabels[category as keyof typeof categoryLabels]}
                      </h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {visibleInCategory}/{totalInCategory}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSelectAll(category, visibleInCategory < totalInCategory)}
                        >
                          {visibleInCategory === totalInCategory ? (
                            <>
                              <EyeOff className="w-4 h-4 mr-1" />
                              Ocultar Todos
                            </>
                          ) : (
                            <>
                              <Eye className="w-4 h-4 mr-1" />
                              Mostrar Todos
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {categoryColumns.map((column) => (
                        <div
                          key={column.key}
                          className="flex items-center space-x-2 p-3 border rounded-md hover:bg-muted/50 transition-colors"
                        >
                          <Checkbox
                            id={column.key}
                            checked={column.visible}
                            onCheckedChange={(checked) => 
                              handleColumnToggle(column.key, checked as boolean)
                            }
                          />
                          <label
                            htmlFor={column.key}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                          >
                            {column.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>

        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSave}>
            Aplicar Configurações
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ColumnManager;
