
import React, { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Sidebar from '@/components/layout/Sidebar';
import { Button } from "@/components/ui/button";
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Filter, Plus, Search, Truck, User, FileText, MapPin, Edit, Settings } from 'lucide-react';
import { Operation, ClienteEnum, ModalidadeEnum, StatusCarregamentoEnum, StatusDescargaEnum, ColumnConfig } from '@/types';
import { useToast } from '@/hooks/use-toast';
import OperationDialog from '@/components/operations/OperationDialog';
import OperationDetails from '@/components/operations/OperationDetails';
import ColumnManager from '@/components/operations/ColumnManager';
import CustomTable from '@/components/operations/CustomTable';
import { defaultColumnConfig, loadColumnConfig, saveColumnConfig } from '@/components/operations/columnConfig';

// Mock data for operations baseado na planilha
const initialOperations: Operation[] = [
  {
    chave: "413",
    dataHoraAgendadaCarregamento: "05/06/2025 07:00:00",
    dataHoraAgendadaDescarga: "09/06/2025 07:00:00",
    produto: "PRODUTOS ALIMENTICIOS",
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: "DT 11763733",
    clienteCidadeOrigem: "Cabreúva",
    ufOrigem: "SP",
    clienteCidadeDestino: "Anápolis",
    ufDestino: "GO",
    peso: "16 TON",
    motorista: "RODRIGO CALDAS",
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 4,
    modeloEquipamento: "Truck",
    operacaoCadastrada: true,
    programador: "Paulo/Gabriel"
  },
  {
    chave: "426",
    dataHoraAgendadaCarregamento: "04/06/2025 16:00:00",
    dataHoraAgendadaDescarga: "06/06/2025 22:00:00",
    produto: "PRODUTOS ALIMENTICIOS BITREM",
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: "DT 11786573",
    clienteCidadeOrigem: "Sete Lagoas",
    ufOrigem: "MG",
    clienteCidadeDestino: "Cabreúva",
    ufDestino: "SP",
    peso: "4 TON",
    motorista: "PEDRO GALVAO LEMOS",
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 10,
    modeloEquipamento: "Bitrem",
    operacaoCadastrada: true,
    codigoDaOperacao: "308622880",
    freteEmpresaNet: "R$ 5.000,00",
    numeroSolicitacaoCarga: "139563",
    placaTracao: "RCH5D99",
    programador: "Paulo/Gabriel",
    dataHoraChegadaCarga: "04/06/2025 13:24:00",
    dataHoraInicioCarga: "04/06/2025 13:24:00",
    statusFinalCarregamento: StatusCarregamentoEnum.CARREGANDO,
    performanceCarregamento: "NO PRAZO",
    causaAtrazoCarregamento: "Não houve atrazo"
  },
  {
    chave: "427",
    dataHoraAgendadaCarregamento: "04/06/2025 16:00:00",
    dataHoraAgendadaDescarga: "06/06/2025 22:00:00",
    produto: "PRODUTOS ALIMENTICIOS BITREM",
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: "DT 11786572",
    clienteCidadeOrigem: "Sete Lagoas",
    ufOrigem: "MG",
    clienteCidadeDestino: "Cabreúva",
    ufDestino: "SP",
    peso: "4 TON",
    motorista: "PEDRO GALVAO LEMOS",
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 10,
    modeloEquipamento: "Bitrem",
    operacaoCadastrada: true,
    codigoDaOperacao: "308622880",
    freteEmpresaNet: "R$ 5.000,00",
    numeroSolicitacaoCarga: "139562",
    placaTracao: "RCH5D99",
    programador: "Paulo/Gabriel",
    dataHoraChegadaCarga: "04/06/2025 13:24:00",
    dataHoraInicioCarga: "04/06/2025 13:24:00",
    statusFinalCarregamento: StatusCarregamentoEnum.CARREGANDO,
    performanceCarregamento: "NO PRAZO",
    causaAtrazoCarregamento: "Não houve atrazo"
  }
];

// Mock data for available operators and equipment
const availableOperators = [
  { id: 'OP001', name: 'RODRIGO CALDAS' },
  { id: 'OP002', name: 'PEDRO GALVAO LEMOS' },
  { id: 'OP003', name: 'Carlos Silva' },
  { id: 'OP004', name: 'Maria Oliveira' },
  { id: 'OP005', name: 'João Pereira' }
];

const availableEquipments = [
  { id: 'EQ001', model: 'Truck' },
  { id: 'EQ002', model: 'Bitrem' },
  { id: 'EQ003', model: 'Carreta' },
  { id: 'EQ004', model: 'Toco' }
];

const availableProgrammers = [
  'Paulo/Gabriel',
  'Ana/Carlos',
  'Maria/João'
];

const OperationsPage = () => {
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const [search, setSearch] = useState('');
  const [cliente, setCliente] = useState<string>('all');
  const [modalidade, setModalidade] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [operations, setOperations] = useState<Operation[]>(initialOperations);

  // Column configuration state
  const [columnConfig, setColumnConfig] = useState<ColumnConfig[]>(defaultColumnConfig);

  // Dialog states
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [columnManagerOpen, setColumnManagerOpen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState<Operation | null>(null);

  // Load column configuration on component mount
  useEffect(() => {
    const savedConfig = loadColumnConfig();
    setColumnConfig(savedConfig);
  }, []);
  
  // Filter operations based on search and filters
  const filteredOperations = operations.filter(operation => {
    // Search filter
    const matchesSearch = search === '' ||
                          operation.motorista.toLowerCase().includes(search.toLowerCase()) ||
                          operation.chave.toLowerCase().includes(search.toLowerCase()) ||
                          operation.produto.toLowerCase().includes(search.toLowerCase()) ||
                          operation.clienteCidadeOrigem.toLowerCase().includes(search.toLowerCase()) ||
                          operation.clienteCidadeDestino.toLowerCase().includes(search.toLowerCase()) ||
                          operation.idB100SvnDt.toLowerCase().includes(search.toLowerCase()) ||
                          operation.modeloEquipamento.toLowerCase().includes(search.toLowerCase());

    // Cliente filter
    const matchesCliente = cliente === 'all' || operation.clientePagador === cliente;

    // Modalidade filter
    const matchesModalidade = modalidade === 'all' || operation.modalidade === modalidade;

    // Status filter
    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'agendado' && !operation.statusFinalCarregamento) ||
      (statusFilter === 'carregando' && operation.statusFinalCarregamento === StatusCarregamentoEnum.CARREGANDO) ||
      (statusFilter === 'carregado' && operation.statusFinalCarregamento === StatusCarregamentoEnum.CARREGADO) ||
      (statusFilter === 'no_prazo' && operation.statusFinalCarregamento === StatusCarregamentoEnum.NO_PRAZO) ||
      (statusFilter === 'atrasado' && operation.statusFinalCarregamento === StatusCarregamentoEnum.ATRASADO) ||
      (statusFilter === 'cancelado' && operation.statusFinalCarregamento === StatusCarregamentoEnum.CANCELADO);

    return matchesSearch && matchesCliente && matchesModalidade && matchesStatus;
  });

  // Format date/time from Brazilian format
  const formatDateTime = (dateTimeString?: string) => {
    if (!dateTimeString) return '-';
    return dateTimeString;
  };

  // Handle save operation
  const handleSaveOperation = (operationData: Operation) => {
    const isNewOperation = !operations.some(op => op.chave === operationData.chave);

    if (isNewOperation) {
      // Add new operation
      setOperations(prev => [operationData, ...prev]);
      toast({
        title: "Operação criada",
        description: "A operação foi criada com sucesso."
      });
    } else {
      // Update existing operation
      setOperations(prev =>
        prev.map(op => op.chave === operationData.chave ? operationData : op)
      );
      toast({
        title: "Operação atualizada",
        description: "A operação foi atualizada com sucesso."
      });
    }
  };

  // Open details dialog
  const handleViewDetails = (operation: Operation) => {
    setSelectedOperation(operation);
    setDetailsDialogOpen(true);
  };

  // Open edit dialog from details
  const handleEditFromDetails = () => {
    setDetailsDialogOpen(false);
    setEditDialogOpen(true);
  };

  // Handle column configuration changes
  const handleColumnsChange = (newColumns: ColumnConfig[]) => {
    setColumnConfig(newColumns);
    saveColumnConfig(newColumns);
    toast({
      title: "Configuração salva",
      description: "As configurações de colunas foram salvas com sucesso."
    });
  };

  return (
    <div className="flex min-h-screen bg-background">
      <Sidebar />
      
      <div className={cn(
        "flex-1 flex flex-col",
        !isMobile && "ml-64" // Offset for sidebar when not mobile
      )}>
        <Navbar 
          title="Operações" 
          subtitle="Controle de Operações"
        />
        
        <main className="flex-1 px-6 py-6">
          {/* Header with search and actions */}
          <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                type="text"
                placeholder="Buscar por chave, motorista, produto, cidade..."
                className="pl-10"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => setColumnManagerOpen(true)}
              >
                <Settings className="w-4 h-4" />
                Configurar Colunas
              </Button>
              <Button
                className="gap-2"
                onClick={() => {
                  setSelectedOperation(null);
                  setAddDialogOpen(true);
                }}
              >
                <Plus className="w-4 h-4" />
                Nova Operação
              </Button>
            </div>
          </div>

          {/* Advanced filters */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Cliente</h4>
              <Select value={cliente} onValueChange={setCliente}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os Clientes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Clientes</SelectItem>
                  <SelectItem value={ClienteEnum.NOVELIS}>{ClienteEnum.NOVELIS}</SelectItem>
                  <SelectItem value={ClienteEnum.PEPSICO}>{ClienteEnum.PEPSICO}</SelectItem>
                  <SelectItem value={ClienteEnum.AMBEV}>{ClienteEnum.AMBEV}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Modalidade</h4>
              <Select value={modalidade} onValueChange={setModalidade}>
                <SelectTrigger>
                  <SelectValue placeholder="Todas as Modalidades" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as Modalidades</SelectItem>
                  <SelectItem value={ModalidadeEnum.FROTA}>{ModalidadeEnum.FROTA}</SelectItem>
                  <SelectItem value={ModalidadeEnum.TERCEIRO}>{ModalidadeEnum.TERCEIRO}</SelectItem>
                  <SelectItem value={ModalidadeEnum.AGREGADO}>{ModalidadeEnum.AGREGADO}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Status</h4>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  <SelectItem value="agendado">Agendado</SelectItem>
                  <SelectItem value="carregando">Carregando</SelectItem>
                  <SelectItem value="carregado">Carregado</SelectItem>
                  <SelectItem value="no_prazo">No Prazo</SelectItem>
                  <SelectItem value="atrasado">Atrasado</SelectItem>
                  <SelectItem value="cancelado">Cancelado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Resultados</h4>
              <div className="flex items-center h-10 px-3 py-2 border rounded-md bg-muted/30">
                <span className="text-sm text-muted-foreground">
                  {filteredOperations.length} operação{filteredOperations.length !== 1 ? 'ões' : ''} encontrada{filteredOperations.length !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
          </div>
          
          {/* Active Operations */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">Operações em Andamento</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredOperations
                .filter(op => op.statusFinalCarregamento === StatusCarregamentoEnum.CARREGANDO || !op.statusFinalCarregamento)
                .map((operation) => (
                  <div key={operation.chave} className="bg-card border rounded-lg overflow-hidden shadow">
                    <div className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h3 className="font-medium">{operation.motorista}</h3>
                          <p className="text-sm text-muted-foreground">Chave: {operation.chave}</p>
                        </div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs ${
                          operation.statusFinalCarregamento === StatusCarregamentoEnum.CARREGADO || operation.statusFinalCarregamento === StatusCarregamentoEnum.NO_PRAZO
                            ? 'bg-status-operational/10 text-status-operational'
                            : operation.statusFinalCarregamento === StatusCarregamentoEnum.CARREGANDO
                            ? 'bg-status-maintenance/10 text-status-maintenance'
                            : operation.statusFinalCarregamento === StatusCarregamentoEnum.ATRASADO
                            ? 'bg-status-warning/10 text-status-warning'
                            : 'bg-muted text-muted-foreground'
                        }`}>
                          {operation.statusFinalCarregamento || 'Agendado'}
                        </span>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Truck className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{operation.modeloEquipamento}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{operation.clientePagador}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{operation.clienteCidadeOrigem}/{operation.ufOrigem} → {operation.clienteCidadeDestino}/{operation.ufDestino}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">Carregamento: {formatDateTime(operation.dataHoraAgendadaCarregamento)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="border-t px-4 py-3 bg-muted/30 flex justify-between items-center">
                      <span className="text-sm">Peso: {operation.peso}</span>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetails(operation)}
                        >
                          Detalhes
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedOperation(operation);
                            setEditDialogOpen(true);
                          }}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

              {filteredOperations.filter(op => op.statusFinalCarregamento === StatusCarregamentoEnum.CARREGANDO || !op.statusFinalCarregamento).length === 0 && (
                <div className="col-span-full p-8 text-center bg-card border rounded-lg">
                  <p className="text-muted-foreground">Nenhuma operação em andamento</p>
                </div>
              )}
            </div>
          </div>
          
          {/* All Operations Table */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">Todas as Operações</h2>
            <CustomTable
              operations={filteredOperations}
              columns={columnConfig}
              onViewDetails={handleViewDetails}
              onEdit={(operation) => {
                setSelectedOperation(operation);
                setEditDialogOpen(true);
              }}
            />
          </div>
        </main>
      </div>

      {/* Add Operation Dialog */}
      <OperationDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onSave={handleSaveOperation}
        availableOperators={availableOperators}
        availableEquipments={availableEquipments}
        availableProgrammers={availableProgrammers}
      />

      {/* Edit Operation Dialog */}
      <OperationDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        operation={selectedOperation || undefined}
        onSave={handleSaveOperation}
        availableOperators={availableOperators}
        availableEquipments={availableEquipments}
        availableProgrammers={availableProgrammers}
      />

      {/* Operation Details Dialog */}
      <OperationDetails
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        operation={selectedOperation}
        onEdit={handleEditFromDetails}
      />

      {/* Column Manager Dialog */}
      <ColumnManager
        open={columnManagerOpen}
        onOpenChange={setColumnManagerOpen}
        columns={columnConfig}
        onColumnsChange={handleColumnsChange}
      />
    </div>
  );
};

export default OperationsPage;
