# 📊 Sistema de Colunas Customizáveis - Operações

## 🎯 Visão Geral

O sistema de colunas customizáveis permite que os usuários escolham quais campos das operações serão exibidos na tabela principal, proporcionando uma experiência personalizada e focada nas informações mais relevantes para cada usuário.

## ✨ Funcionalidades

### 🔧 Configuração de Colunas
- **Interface Intuitiva**: Diálogo organizado por categorias para fácil navegação
- **Seleção Flexível**: Marque/desmarque colunas individualmente
- **Controle por Categoria**: Mostre/oculte todas as colunas de uma categoria
- **Contador Visual**: Veja quantas colunas estão selecionadas
- **Restaurar Padrão**: Volte às configurações originais com um clique

### 💾 Persistência
- **Salvamento Automático**: Configurações são salvas no localStorage
- **Carregamento Automático**: Suas preferências são restauradas ao recarregar a página
- **Feedback Visual**: Notificação quando as configurações são salvas

### 📋 Categorias de Campos

#### 🆔 **Identificação**
- Chave
- Cliente
- ID/DT

#### 📅 **Agendamento**
- Agendamento Carga
- Agendamento Descarga

#### 📦 **Produto e Rota**
- Produto
- Peso

#### 🗺️ **Origem e Destino**
- Cidade Origem
- UF Origem
- Cidade Destino
- UF Destino

#### 👨‍💼 **Motorista e Equipamento**
- Motorista
- Modalidade
- Equipamento
- Dias em Rota

#### ⚙️ **Operação e Códigos**
- Op. Cadastrada
- Código Operação
- Nº Rota
- Nº Sol. Carga
- Placa
- Programador

#### 💰 **Financeiro**
- Frete Empresa
- Frete Terceiro
- Margem Terceiro
- Frete Agregado
- Margem Agregado

#### 🚛 **Carregamento**
- App Cliente Carga
- Chegada Carga
- Início Carga
- Fim Carregamento
- Tempo Carga
- Status Carregamento
- Performance Carga
- Causa Atraso Carga
- Diárias C
- Nº Ocorrência Carga

#### 📤 **Descarga**
- App Cliente Descarga
- Chegada Descarga
- Início Descarga
- Fim Descarga
- Tempo Descarga
- Status Descarga
- Performance Descarga
- Causa Atraso Descarga
- Diárias D
- Nº Ocorrência Descarga
- Houve Descarga
- Devolução Pallet
- Houve Avaria
- Houve Devolução
- Comprovante Descarga

#### 📄 **Documentos**
- Nº Nota Fiscal
- Nº CTE
- Observação

## 🚀 Como Usar

### 1. **Acessar o Configurador**
1. Vá para a página de **Operações**
2. Clique no botão **"Configurar Colunas"** (ícone de engrenagem)
3. O diálogo de configuração será aberto

### 2. **Configurar Colunas**
1. **Por Categoria**: Use os botões "Mostrar Todos" / "Ocultar Todos" para cada categoria
2. **Individual**: Marque/desmarque caixas específicas para cada campo
3. **Visualizar**: Veja o contador de colunas selecionadas no topo
4. **Restaurar**: Use "Restaurar Padrão" para voltar às configurações originais

### 3. **Aplicar Configurações**
1. Clique em **"Aplicar Configurações"**
2. A tabela será atualizada instantaneamente
3. Uma notificação confirmará que as configurações foram salvas

### 4. **Configurações Padrão**
Por padrão, as seguintes colunas são exibidas:
- Chave
- Cliente
- Motorista
- Cidade Origem
- Cidade Destino
- Produto
- Peso
- Modalidade
- Status Carregamento

## 🎨 Recursos Visuais

### 📊 **Renderização Inteligente**
- **Status**: Badges coloridos para diferentes estados
- **Datas**: Formatação brasileira automática
- **Valores**: Formatação monetária com fonte mono
- **Booleanos**: Indicadores visuais Sim/Não
- **Modalidades**: Badges distintivos por tipo

### 📱 **Responsividade**
- **Scroll Horizontal**: Tabela rola horizontalmente em telas pequenas
- **Larguras Fixas**: Colunas mantêm larguras consistentes
- **Adaptação Mobile**: Interface otimizada para dispositivos móveis

## 🔧 Implementação Técnica

### 📁 **Arquivos Principais**
```
src/components/operations/
├── ColumnManager.tsx      # Diálogo de configuração
├── CustomTable.tsx       # Tabela customizável
└── columnConfig.ts       # Configurações e utilitários
```

### 🏗️ **Estrutura de Dados**
```typescript
interface ColumnConfig {
  key: keyof Operation;     // Campo da operação
  label: string;           // Rótulo da coluna
  visible: boolean;        // Se está visível
  width?: string;          // Largura da coluna
  category: string;        // Categoria do campo
}
```

### 💾 **Persistência**
- **localStorage**: `operationsTableColumns`
- **Formato**: JSON serializado das configurações
- **Fallback**: Configurações padrão se não houver dados salvos

## 🎯 Benefícios

### 👥 **Para Usuários**
- **Personalização**: Veja apenas as informações relevantes
- **Eficiência**: Reduza a sobrecarga visual
- **Flexibilidade**: Adapte a interface às suas necessidades
- **Produtividade**: Acesso rápido aos dados importantes

### 🏢 **Para a Empresa**
- **Adaptabilidade**: Interface se adapta a diferentes funções
- **Escalabilidade**: Fácil adição de novos campos
- **Usabilidade**: Melhora a experiência do usuário
- **Eficiência Operacional**: Dados relevantes sempre visíveis

## 🔮 Futuras Melhorias

### 📈 **Recursos Planejados**
- **Perfis de Usuário**: Configurações por função/departamento
- **Ordenação**: Arrastar e soltar para reordenar colunas
- **Filtros Avançados**: Filtros específicos por coluna
- **Exportação**: Exportar dados com colunas selecionadas
- **Templates**: Salvar e compartilhar configurações

### 🎨 **Melhorias de UX**
- **Preview**: Visualizar mudanças antes de aplicar
- **Busca**: Buscar campos específicos no configurador
- **Favoritos**: Marcar campos mais utilizados
- **Histórico**: Desfazer/refazer configurações

---

**Desenvolvido para maximizar a eficiência e personalização da experiência do usuário no sistema de logística B2B.**
