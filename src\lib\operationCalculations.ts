import { Operation } from '@/types';

// Função para converter string de data/hora brasileira para Date
export const parseDateTime = (dateTimeString: string): Date | null => {
  if (!dateTimeString || dateTimeString.trim() === '') {
    return null;
  }

  try {
    // Formato esperado: "DD/MM/YYYY HH:mm:ss" ou "DD/MM/YYYY HH:mm"
    const [datePart, timePart] = dateTimeString.split(' ');
    
    if (!datePart || !timePart) {
      return null;
    }

    const [day, month, year] = datePart.split('/').map(Number);
    const [hours, minutes, seconds = 0] = timePart.split(':').map(Number);

    if (isNaN(day) || isNaN(month) || isNaN(year) || isNaN(hours) || isNaN(minutes)) {
      return null;
    }

    return new Date(year, month - 1, day, hours, minutes, seconds || 0);
  } catch (error) {
    console.error('Erro ao converter data/hora:', error);
    return null;
  }
};

// Função para calcular performance de carregamento
export const calculateLoadingPerformance = (
  scheduledDateTime: string,
  arrivalDateTime: string
): string => {
  if (!arrivalDateTime || arrivalDateTime.trim() === '') {
    return ''; // Campo em branco se não houver data de chegada
  }

  const scheduled = parseDateTime(scheduledDateTime);
  const arrival = parseDateTime(arrivalDateTime);

  if (!scheduled || !arrival) {
    return ''; // Campo em branco se não conseguir converter as datas
  }

  // Se chegada for antes ou igual ao agendado = NO PRAZO
  // Se chegada for depois do agendado = ATRASADO
  return arrival <= scheduled ? 'NO PRAZO' : 'ATRASADO';
};

// Função para calcular performance de descarga
export const calculateUnloadingPerformance = (
  scheduledDateTime: string,
  arrivalDateTime: string
): string => {
  if (!arrivalDateTime || arrivalDateTime.trim() === '') {
    return ''; // Campo em branco se não houver data de chegada
  }

  const scheduled = parseDateTime(scheduledDateTime);
  const arrival = parseDateTime(arrivalDateTime);

  if (!scheduled || !arrival) {
    return ''; // Campo em branco se não conseguir converter as datas
  }

  // Se chegada for antes ou igual ao agendado = NO PRAZO
  // Se chegada for depois do agendado = ATRASADO
  return arrival <= scheduled ? 'NO PRAZO' : 'ATRASADO';
};

// Função para calcular tempo entre duas datas
export const calculateTimeDifference = (
  startDateTime: string,
  endDateTime: string
): string => {
  if (!startDateTime || !endDateTime || startDateTime.trim() === '' || endDateTime.trim() === '') {
    return '';
  }

  const start = parseDateTime(startDateTime);
  const end = parseDateTime(endDateTime);

  if (!start || !end) {
    return '';
  }

  const diffMs = end.getTime() - start.getTime();
  
  if (diffMs < 0) {
    return ''; // Não calcular se a data final for antes da inicial
  }

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;

  if (hours === 0) {
    return `${minutes}min`;
  } else if (minutes === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${minutes}min`;
  }
};

// Função para calcular tempo em carga (do início ao fim do carregamento)
export const calculateLoadingTime = (
  startDateTime: string,
  endDateTime: string
): string => {
  return calculateTimeDifference(startDateTime, endDateTime);
};

// Função para calcular tempo de descarga (do início ao fim da descarga)
export const calculateUnloadingTime = (
  startDateTime: string,
  endDateTime: string
): string => {
  return calculateTimeDifference(startDateTime, endDateTime);
};

// Função para calcular diárias de carregamento (atraso do cliente)
export const calculateLoadingDemurrage = (
  scheduledDateTime: string,
  arrivalDateTime: string,
  endDateTime: string
): string => {
  if (!scheduledDateTime || !endDateTime) {
    return ''; // Não pode calcular sem horários essenciais
  }

  const scheduled = parseDateTime(scheduledDateTime);
  const arrival = parseDateTime(arrivalDateTime);
  const end = parseDateTime(endDateTime);

  if (!scheduled || !end) {
    return '';
  }

  // Determinar o horário de início para cálculo da diária
  // Se chegou adiantado, considera o horário agendado
  // Se chegou atrasado, considera o horário de chegada
  let startTime = scheduled;

  if (arrival && arrival > scheduled) {
    // Chegou atrasado, então o atraso é responsabilidade do motorista, não do cliente
    // Mas ainda precisamos calcular a diária do cliente a partir do agendado
    startTime = scheduled;
  }

  // Calcular diferença entre início (agendado ou chegada) e fim
  const diffMs = end.getTime() - startTime.getTime();

  if (diffMs <= 0) {
    return '0h'; // Não houve atraso do cliente
  }

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;

  if (hours === 0 && minutes === 0) {
    return '0h';
  } else if (minutes === 0) {
    return `${hours}h`;
  } else if (hours === 0) {
    return `${minutes}min`;
  } else {
    return `${hours}h ${minutes}min`;
  }
};

// Função para calcular diárias de descarga (atraso do cliente + possível herança do carregamento)
export const calculateUnloadingDemurrage = (
  // Dados do carregamento
  loadingScheduledDateTime: string,
  loadingArrivalDateTime: string,
  loadingEndDateTime: string,

  // Dados da descarga
  unloadingScheduledDateTime: string,
  unloadingArrivalDateTime: string,
  unloadingEndDateTime: string
): string => {
  if (!unloadingScheduledDateTime || !unloadingEndDateTime) {
    return ''; // Não pode calcular sem horários essenciais da descarga
  }

  const unloadingScheduled = parseDateTime(unloadingScheduledDateTime);
  const unloadingArrival = parseDateTime(unloadingArrivalDateTime);
  const unloadingEnd = parseDateTime(unloadingEndDateTime);

  if (!unloadingScheduled || !unloadingEnd) {
    return '';
  }

  // Calcular atraso herdado do carregamento
  let inheritedDelay = 0; // em minutos

  if (loadingScheduledDateTime && loadingEndDateTime) {
    const loadingScheduled = parseDateTime(loadingScheduledDateTime);
    const loadingEnd = parseDateTime(loadingEndDateTime);
    const loadingArrival = parseDateTime(loadingArrivalDateTime);

    if (loadingScheduled && loadingEnd) {
      // Calcular quanto tempo o carregamento atrasou além do agendado
      let loadingStartForCalculation = loadingScheduled;

      // Se chegou atrasado no carregamento, isso não é culpa do cliente do carregamento
      // Mas pode afetar o horário de chegada na descarga
      if (loadingArrival && loadingArrival > loadingScheduled) {
        // O atraso na chegada não é culpa do cliente, mas o tempo extra de carregamento sim
        loadingStartForCalculation = loadingScheduled;
      }

      const loadingDelayMs = loadingEnd.getTime() - loadingStartForCalculation.getTime();
      if (loadingDelayMs > 0) {
        inheritedDelay = Math.floor(loadingDelayMs / (1000 * 60));
      }
    }
  }

  // Determinar horário de início para cálculo da diária de descarga
  let unloadingStartTime = unloadingScheduled;

  // Se chegou atrasado na descarga por causa do carregamento, ajustar o horário agendado
  if (unloadingArrival && inheritedDelay > 0) {
    // Ajustar o horário agendado considerando o atraso herdado
    const adjustedScheduled = new Date(unloadingScheduled.getTime() + (inheritedDelay * 60 * 1000));

    if (unloadingArrival <= adjustedScheduled) {
      // Chegou no horário considerando o atraso herdado
      unloadingStartTime = adjustedScheduled;
    } else {
      // Chegou ainda mais atrasado, usar horário de chegada
      unloadingStartTime = unloadingArrival;
    }
  } else if (unloadingArrival && unloadingArrival > unloadingScheduled) {
    // Chegou atrasado sem herança do carregamento
    unloadingStartTime = unloadingArrival;
  }

  // Calcular diferença entre início ajustado e fim da descarga
  const diffMs = unloadingEnd.getTime() - unloadingStartTime.getTime();

  if (diffMs <= 0) {
    return '0h'; // Não houve atraso do cliente
  }

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;

  if (hours === 0 && minutes === 0) {
    return '0h';
  } else if (minutes === 0) {
    return `${hours}h`;
  } else if (hours === 0) {
    return `${minutes}min`;
  } else {
    return `${hours}h ${minutes}min`;
  }
};

// Função para determinar causa do atraso automaticamente
export const calculateDelayReason = (
  scheduledDateTime: string,
  arrivalDateTime: string,
  currentReason?: string
): string => {
  // Se já existe uma causa personalizada, mantém
  if (currentReason && currentReason.trim() !== '') {
    return currentReason;
  }

  const performance = calculateLoadingPerformance(scheduledDateTime, arrivalDateTime);
  
  if (performance === 'ATRASADO') {
    return 'Atraso na chegada';
  } else if (performance === 'NO PRAZO') {
    return 'Não houve atraso';
  }

  return '';
};

// Função principal para calcular todos os campos automaticamente
export const calculateOperationFields = (operation: Partial<Operation>): Partial<Operation> => {
  const updatedOperation = { ...operation };

  // Calcular performance de carregamento
  if (operation.dataHoraAgendadaCarregamento && operation.dataHoraChegadaCarga) {
    updatedOperation.performanceCarregamento = calculateLoadingPerformance(
      operation.dataHoraAgendadaCarregamento,
      operation.dataHoraChegadaCarga
    );

    // Calcular causa do atraso de carregamento
    updatedOperation.causaAtrazoCarregamento = calculateDelayReason(
      operation.dataHoraAgendadaCarregamento,
      operation.dataHoraChegadaCarga,
      operation.causaAtrazoCarregamento
    );
  }

  // Calcular performance de descarga
  if (operation.dataHoraAgendadaDescarga && operation.dataHoraChegadaDescarga) {
    updatedOperation.performanceDescarga = calculateUnloadingPerformance(
      operation.dataHoraAgendadaDescarga,
      operation.dataHoraChegadaDescarga
    );

    // Calcular causa do atraso de descarga
    updatedOperation.causaAtrazoDescarga = calculateDelayReason(
      operation.dataHoraAgendadaDescarga,
      operation.dataHoraChegadaDescarga,
      operation.causaAtrazoDescarga
    );
  }

  // Calcular tempo em carga
  if (operation.dataHoraInicioCarga && operation.dataHoraFimCarregamento) {
    updatedOperation.tempoEmCarga = calculateLoadingTime(
      operation.dataHoraInicioCarga,
      operation.dataHoraFimCarregamento
    );
  }

  // Calcular tempo de descarga
  if (operation.dataHoraInicioDescarga && operation.dataHoraFimDescarga) {
    updatedOperation.tempoDescarga = calculateUnloadingTime(
      operation.dataHoraInicioDescarga,
      operation.dataHoraFimDescarga
    );
  }

  // Calcular diárias de carregamento (atraso do cliente)
  if (operation.dataHoraAgendadaCarregamento && operation.dataHoraFimCarregamento) {
    updatedOperation.diariasC = calculateLoadingDemurrage(
      operation.dataHoraAgendadaCarregamento,
      operation.dataHoraChegadaCarga || '',
      operation.dataHoraFimCarregamento
    );
  }

  // Calcular diárias de descarga (atraso do cliente + possível herança)
  if (operation.dataHoraAgendadaDescarga && operation.dataHoraFimDescarga) {
    updatedOperation.diariasD = calculateUnloadingDemurrage(
      // Dados do carregamento
      operation.dataHoraAgendadaCarregamento || '',
      operation.dataHoraChegadaCarga || '',
      operation.dataHoraFimCarregamento || '',

      // Dados da descarga
      operation.dataHoraAgendadaDescarga,
      operation.dataHoraChegadaDescarga || '',
      operation.dataHoraFimDescarga
    );
  }

  return updatedOperation;
};

// Função para formatar data/hora para exibição
export const formatDateTime = (dateTimeString: string): string => {
  if (!dateTimeString || dateTimeString.trim() === '') {
    return '-';
  }
  return dateTimeString;
};

// Função para validar formato de data/hora
export const isValidDateTime = (dateTimeString: string): boolean => {
  if (!dateTimeString || dateTimeString.trim() === '') {
    return true; // Campo vazio é válido
  }

  const parsed = parseDateTime(dateTimeString);
  return parsed !== null;
};
