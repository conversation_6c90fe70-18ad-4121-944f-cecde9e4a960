import { Operation } from '@/types';

// Função para converter string de data/hora brasileira para Date
export const parseDateTime = (dateTimeString: string): Date | null => {
  if (!dateTimeString || dateTimeString.trim() === '') {
    return null;
  }

  try {
    // Formato esperado: "DD/MM/YYYY HH:mm:ss" ou "DD/MM/YYYY HH:mm"
    const [datePart, timePart] = dateTimeString.split(' ');
    
    if (!datePart || !timePart) {
      return null;
    }

    const [day, month, year] = datePart.split('/').map(Number);
    const [hours, minutes, seconds = 0] = timePart.split(':').map(Number);

    if (isNaN(day) || isNaN(month) || isNaN(year) || isNaN(hours) || isNaN(minutes)) {
      return null;
    }

    return new Date(year, month - 1, day, hours, minutes, seconds || 0);
  } catch (error) {
    console.error('Erro ao converter data/hora:', error);
    return null;
  }
};

// Função para calcular performance de carregamento
export const calculateLoadingPerformance = (
  scheduledDateTime: string,
  arrivalDateTime: string
): string => {
  if (!arrivalDateTime || arrivalDateTime.trim() === '') {
    return ''; // Campo em branco se não houver data de chegada
  }

  const scheduled = parseDateTime(scheduledDateTime);
  const arrival = parseDateTime(arrivalDateTime);

  if (!scheduled || !arrival) {
    return ''; // Campo em branco se não conseguir converter as datas
  }

  // Se chegada for antes ou igual ao agendado = NO PRAZO
  // Se chegada for depois do agendado = ATRASADO
  return arrival <= scheduled ? 'NO PRAZO' : 'ATRASADO';
};

// Função para calcular performance de descarga
export const calculateUnloadingPerformance = (
  scheduledDateTime: string,
  arrivalDateTime: string
): string => {
  if (!arrivalDateTime || arrivalDateTime.trim() === '') {
    return ''; // Campo em branco se não houver data de chegada
  }

  const scheduled = parseDateTime(scheduledDateTime);
  const arrival = parseDateTime(arrivalDateTime);

  if (!scheduled || !arrival) {
    return ''; // Campo em branco se não conseguir converter as datas
  }

  // Se chegada for antes ou igual ao agendado = NO PRAZO
  // Se chegada for depois do agendado = ATRASADO
  return arrival <= scheduled ? 'NO PRAZO' : 'ATRASADO';
};

// Função para calcular tempo entre duas datas
export const calculateTimeDifference = (
  startDateTime: string,
  endDateTime: string
): string => {
  if (!startDateTime || !endDateTime || startDateTime.trim() === '' || endDateTime.trim() === '') {
    return '';
  }

  const start = parseDateTime(startDateTime);
  const end = parseDateTime(endDateTime);

  if (!start || !end) {
    return '';
  }

  const diffMs = end.getTime() - start.getTime();
  
  if (diffMs < 0) {
    return ''; // Não calcular se a data final for antes da inicial
  }

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;

  if (hours === 0) {
    return `${minutes}min`;
  } else if (minutes === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${minutes}min`;
  }
};

// Função para calcular tempo em carga (do início ao fim do carregamento)
export const calculateLoadingTime = (
  startDateTime: string,
  endDateTime: string
): string => {
  return calculateTimeDifference(startDateTime, endDateTime);
};

// Função para calcular tempo de descarga (do início ao fim da descarga)
export const calculateUnloadingTime = (
  startDateTime: string,
  endDateTime: string
): string => {
  return calculateTimeDifference(startDateTime, endDateTime);
};

// Função para determinar causa do atraso automaticamente
export const calculateDelayReason = (
  scheduledDateTime: string,
  arrivalDateTime: string,
  currentReason?: string
): string => {
  // Se já existe uma causa personalizada, mantém
  if (currentReason && currentReason.trim() !== '') {
    return currentReason;
  }

  const performance = calculateLoadingPerformance(scheduledDateTime, arrivalDateTime);
  
  if (performance === 'ATRASADO') {
    return 'Atraso na chegada';
  } else if (performance === 'NO PRAZO') {
    return 'Não houve atraso';
  }

  return '';
};

// Função principal para calcular todos os campos automaticamente
export const calculateOperationFields = (operation: Partial<Operation>): Partial<Operation> => {
  const updatedOperation = { ...operation };

  // Calcular performance de carregamento
  if (operation.dataHoraAgendadaCarregamento && operation.dataHoraChegadaCarga) {
    updatedOperation.performanceCarregamento = calculateLoadingPerformance(
      operation.dataHoraAgendadaCarregamento,
      operation.dataHoraChegadaCarga
    );

    // Calcular causa do atraso de carregamento
    updatedOperation.causaAtrazoCarregamento = calculateDelayReason(
      operation.dataHoraAgendadaCarregamento,
      operation.dataHoraChegadaCarga,
      operation.causaAtrazoCarregamento
    );
  }

  // Calcular performance de descarga
  if (operation.dataHoraAgendadaDescarga && operation.dataHoraChegadaDescarga) {
    updatedOperation.performanceDescarga = calculateUnloadingPerformance(
      operation.dataHoraAgendadaDescarga,
      operation.dataHoraChegadaDescarga
    );

    // Calcular causa do atraso de descarga
    updatedOperation.causaAtrazoDescarga = calculateDelayReason(
      operation.dataHoraAgendadaDescarga,
      operation.dataHoraChegadaDescarga,
      operation.causaAtrazoDescarga
    );
  }

  // Calcular tempo em carga
  if (operation.dataHoraInicioCarga && operation.dataHoraFimCarregamento) {
    updatedOperation.tempoEmCarga = calculateLoadingTime(
      operation.dataHoraInicioCarga,
      operation.dataHoraFimCarregamento
    );
  }

  // Calcular tempo de descarga
  if (operation.dataHoraInicioDescarga && operation.dataHoraFimDescarga) {
    updatedOperation.tempoDescarga = calculateUnloadingTime(
      operation.dataHoraInicioDescarga,
      operation.dataHoraFimDescarga
    );
  }

  return updatedOperation;
};

// Função para formatar data/hora para exibição
export const formatDateTime = (dateTimeString: string): string => {
  if (!dateTimeString || dateTimeString.trim() === '') {
    return '-';
  }
  return dateTimeString;
};

// Função para validar formato de data/hora
export const isValidDateTime = (dateTimeString: string): boolean => {
  if (!dateTimeString || dateTimeString.trim() === '') {
    return true; // Campo vazio é válido
  }

  const parsed = parseDateTime(dateTimeString);
  return parsed !== null;
};
