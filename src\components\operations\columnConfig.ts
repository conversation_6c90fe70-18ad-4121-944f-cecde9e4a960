import { ColumnConfig } from '@/types';

export const defaultColumnConfig: ColumnConfig[] = [
  // Identificação
  { key: 'chave', label: 'Chave', visible: true, width: '100px', category: 'identificacao' },
  { key: 'clientePagador', label: 'Cliente', visible: true, width: '120px', category: 'identificacao' },
  { key: 'idB100SvnDt', label: 'ID/DT', visible: false, width: '120px', category: 'identificacao' },

  // Agendamento
  { key: 'dataHoraAgendadaCarregamento', label: 'Agendamento Carga', visible: false, width: '160px', category: 'agendamento' },
  { key: 'dataHoraAgendadaDescarga', label: 'Agendamento Descarga', visible: false, width: '160px', category: 'agendamento' },

  // Produto e Rota
  { key: 'produto', label: 'Produto', visible: true, width: '200px', category: 'produto' },
  { key: 'peso', label: 'Peso', visible: true, width: '100px', category: 'produto' },

  // Origem e Destino
  { key: 'clienteCidadeOrigem', label: 'Cidade Origem', visible: true, width: '140px', category: 'rota' },
  { key: 'ufOrigem', label: 'UF Origem', visible: false, width: '80px', category: 'rota' },
  { key: 'clienteCidadeDestino', label: 'Cidade Destino', visible: true, width: '140px', category: 'rota' },
  { key: 'ufDestino', label: 'UF Destino', visible: false, width: '80px', category: 'rota' },

  // Motorista e Equipamento
  { key: 'motorista', label: 'Motorista', visible: true, width: '160px', category: 'motorista' },
  { key: 'modalidade', label: 'Modalidade', visible: true, width: '120px', category: 'motorista' },
  { key: 'modeloEquipamento', label: 'Equipamento', visible: false, width: '120px', category: 'motorista' },
  { key: 'diasEmRota', label: 'Dias em Rota', visible: false, width: '100px', category: 'motorista' },

  // Operação e Códigos
  { key: 'operacaoCadastrada', label: 'Op. Cadastrada', visible: false, width: '120px', category: 'operacao' },
  { key: 'codigoDaOperacao', label: 'Código Operação', visible: false, width: '140px', category: 'operacao' },
  { key: 'numeroRota', label: 'Nº Rota', visible: false, width: '100px', category: 'operacao' },
  { key: 'numeroSolicitacaoCarga', label: 'Nº Sol. Carga', visible: false, width: '120px', category: 'operacao' },
  { key: 'placaTracao', label: 'Placa', visible: false, width: '100px', category: 'operacao' },
  { key: 'programador', label: 'Programador', visible: false, width: '120px', category: 'operacao' },

  // Financeiro
  { key: 'freteEmpresaNet', label: 'Frete Empresa', visible: false, width: '120px', category: 'financeiro' },
  { key: 'freteTerceiro', label: 'Frete Terceiro', visible: false, width: '120px', category: 'financeiro' },
  { key: 'margemTerceiro', label: 'Margem Terceiro', visible: false, width: '120px', category: 'financeiro' },
  { key: 'freteAgregado', label: 'Frete Agregado', visible: false, width: '120px', category: 'financeiro' },
  { key: 'margemAgregado', label: 'Margem Agregado', visible: false, width: '120px', category: 'financeiro' },

  // Carregamento
  { key: 'appClienteCarga', label: 'App Cliente Carga', visible: false, width: '140px', category: 'carregamento' },
  { key: 'dataHoraChegadaCarga', label: 'Chegada Carga', visible: false, width: '140px', category: 'carregamento' },
  { key: 'dataHoraInicioCarga', label: 'Início Carga', visible: false, width: '140px', category: 'carregamento' },
  { key: 'dataHoraFimCarregamento', label: 'Fim Carregamento', visible: false, width: '140px', category: 'carregamento' },
  { key: 'tempoEmCarga', label: 'Tempo Carga', visible: false, width: '120px', category: 'carregamento' },
  { key: 'statusFinalCarregamento', label: 'Status Carregamento', visible: true, width: '140px', category: 'carregamento' },
  { key: 'performanceCarregamento', label: 'Performance Carga', visible: false, width: '140px', category: 'carregamento' },
  { key: 'causaAtrazoCarregamento', label: 'Causa Atraso Carga', visible: false, width: '160px', category: 'carregamento' },
  { key: 'diariasC', label: 'Diárias C', visible: false, width: '100px', category: 'carregamento' },
  { key: 'numeroOcorrenciaCarga', label: 'Nº Ocorrência Carga', visible: false, width: '140px', category: 'carregamento' },

  // Descarga
  { key: 'appClienteDescarga', label: 'App Cliente Descarga', visible: false, width: '140px', category: 'descarga' },
  { key: 'dataHoraChegadaDescarga', label: 'Chegada Descarga', visible: false, width: '140px', category: 'descarga' },
  { key: 'dataHoraInicioDescarga', label: 'Início Descarga', visible: false, width: '140px', category: 'descarga' },
  { key: 'dataHoraFimDescarga', label: 'Fim Descarga', visible: false, width: '140px', category: 'descarga' },
  { key: 'tempoDescarga', label: 'Tempo Descarga', visible: false, width: '120px', category: 'descarga' },
  { key: 'statusFinal', label: 'Status Descarga', visible: false, width: '140px', category: 'descarga' },
  { key: 'performanceDescarga', label: 'Performance Descarga', visible: false, width: '140px', category: 'descarga' },
  { key: 'causaAtrazoDescarga', label: 'Causa Atraso Descarga', visible: false, width: '160px', category: 'descarga' },
  { key: 'diariasD', label: 'Diárias D', visible: false, width: '100px', category: 'descarga' },
  { key: 'numeroOcorrenciaDescarga', label: 'Nº Ocorrência Descarga', visible: false, width: '140px', category: 'descarga' },
  { key: 'houveDescarga', label: 'Houve Descarga', visible: false, width: '120px', category: 'descarga' },
  { key: 'devolucaoPallet', label: 'Devolução Pallet', visible: false, width: '140px', category: 'descarga' },
  { key: 'houveAvaria', label: 'Houve Avaria', visible: false, width: '120px', category: 'descarga' },
  { key: 'houveDevolucao', label: 'Houve Devolução', visible: false, width: '120px', category: 'descarga' },
  { key: 'comprovanteDescarga', label: 'Comprovante Descarga', visible: false, width: '160px', category: 'descarga' },

  // Documentos
  { key: 'numeroNotaFiscal', label: 'Nº Nota Fiscal', visible: false, width: '140px', category: 'documentos' },
  { key: 'numeroCTE', label: 'Nº CTE', visible: false, width: '120px', category: 'documentos' },

  // Observações
  { key: 'observacao', label: 'Observação', visible: false, width: '200px', category: 'documentos' },
];

// Função para obter colunas visíveis
export const getVisibleColumns = (columnConfig: ColumnConfig[]) => {
  return columnConfig.filter(col => col.visible);
};

// Função para salvar configuração no localStorage
export const saveColumnConfig = (config: ColumnConfig[]) => {
  localStorage.setItem('operationsTableColumns', JSON.stringify(config));
};

// Função para carregar configuração do localStorage
export const loadColumnConfig = (): ColumnConfig[] => {
  const saved = localStorage.getItem('operationsTableColumns');
  if (saved) {
    try {
      return JSON.parse(saved);
    } catch (error) {
      console.error('Erro ao carregar configuração de colunas:', error);
    }
  }
  return defaultColumnConfig;
};
